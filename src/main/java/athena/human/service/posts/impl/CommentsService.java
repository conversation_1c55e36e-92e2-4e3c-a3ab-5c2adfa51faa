package athena.human.service.posts.impl;

import athena.human.domain.data.dto.posts.CommentsDto;
import athena.human.domain.services.posts.ICommentsDomain;
import athena.human.service.posts.ICommentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CommentsService implements ICommentsService {

    @Autowired
    private ICommentsDomain commentsDomain;

    @Override
    public CommentsDto getComment(Long id) {
        return commentsDomain.getComment(id);
    }

    @Override
    public List<CommentsDto> getCommentsForPost(Long postId) {
        return commentsDomain.getCommentsForPost(postId);
    }

    @Override
    public List<CommentsDto> getAllComments() {
        return commentsDomain.getAllComments();
    }

    @Override
    public CommentsDto createComment(CommentsDto commentsDto) {
        return commentsDomain.createComment(commentsDto);
    }

    @Override
    public CommentsDto updateComment(Long id, CommentsDto commentsDto) {
        return commentsDomain.updateComment(id, commentsDto);
    }

    @Override
    public void deleteComment(Long id) {
        commentsDomain.deleteComment(id);
    }
}
