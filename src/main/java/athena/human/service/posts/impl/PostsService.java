package athena.human.service.posts.impl;

import athena.human.domain.data.dto.posts.PostsDto;
import athena.human.domain.services.posts.IPostsDomain;
import athena.human.service.posts.IPostsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class PostsService implements IPostsService {

    @Autowired
    private IPostsDomain postsDomain;

    @Override
    public PostsDto getPost(Long id) {
        return postsDomain.getPost(id);
    }

    @Override
    public List<PostsDto> getAllPosts() {
        return postsDomain.getAllPosts();
    }

    @Override
    public PostsDto createPost(PostsDto postsDto) {
        return postsDomain.createPost(postsDto);
    }

    @Override
    public PostsDto updatePost(Long id, PostsDto postsDto) {
        return postsDomain.updatePost(id, postsDto);
    }

    @Override
    public void deletePost(Long id) {
        postsDomain.deletePost(id);
    }
}
