package athena.human.controller.posts;

import athena.human.domain.data.dto.posts.PostsDto;
import athena.human.service.posts.IPostsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/posts")
public class PostsController {

    @Autowired
    private IPostsService postsService;

    @GetMapping("/{id}")
    public ResponseEntity<PostsDto> getPostById(@PathVariable Long id) {
        return new ResponseEntity<>(postsService.getPost(id), HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity<List<PostsDto>> getAllPosts() {
        return new ResponseEntity<>(postsService.getAllPosts(), HttpStatus.OK);
    }

    @PostMapping
    public ResponseEntity<PostsDto> createPost(@RequestBody PostsDto postsDto) {
        return new ResponseEntity<>(postsService.createPost(postsDto), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<PostsDto> updatePost(@PathVariable Long id, @RequestBody PostsDto postsDto) {
        return new ResponseEntity<>(postsService.updatePost(id, postsDto), HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePost(@PathVariable Long id) {
        postsService.deletePost(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
