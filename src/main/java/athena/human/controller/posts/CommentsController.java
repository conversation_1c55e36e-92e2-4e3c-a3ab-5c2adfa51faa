package athena.human.controller.posts;

import athena.human.domain.data.dto.posts.CommentsDto;
import athena.human.service.posts.ICommentsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/comments")
public class CommentsController {

    @Autowired
    private ICommentsService commentsService;

    @GetMapping("/{id}")
    public ResponseEntity<CommentsDto> getCommentById(@PathVariable Long id) {
        return new ResponseEntity<>(commentsService.getComment(id), HttpStatus.OK);
    }

    @GetMapping("/post/{postId}")
    public ResponseEntity<List<CommentsDto>> getCommentsForPost(@PathVariable Long postId) {
        return new ResponseEntity<>(commentsService.getCommentsForPost(postId), HttpStatus.OK);
    }

    @GetMapping
    public ResponseEntity<List<CommentsDto>> getAllComments() {
        return new ResponseEntity<>(commentsService.getAllComments(), HttpStatus.OK);
    }

    @PostMapping
    public ResponseEntity<CommentsDto> createComment(@RequestBody CommentsDto commentsDto) {
        return new ResponseEntity<>(commentsService.createComment(commentsDto), HttpStatus.CREATED);
    }

    @PutMapping("/{id}")
    public ResponseEntity<CommentsDto> updateComment(@PathVariable Long id, @RequestBody CommentsDto commentsDto) {
        return new ResponseEntity<>(commentsService.updateComment(id, commentsDto), HttpStatus.OK);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteComment(@PathVariable Long id) {
        commentsService.deleteComment(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }
}
