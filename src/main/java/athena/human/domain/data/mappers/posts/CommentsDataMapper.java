package athena.human.domain.data.mappers.posts;

import athena.human.domain.data.dto.posts.CommentsDto;
import athena.human.entity.posts.Comments;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface CommentsDataMapper {

    CommentsDataMapper INSTANCE = Mappers.getMapper(CommentsDataMapper.class);

    CommentsDto toDto(Comments entity);

    Comments toEntity(CommentsDto dto);
}
