package athena.human.domain.data.dto.posts;

import athena.human.domain.data.BaseDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CommentsDto extends BaseDto {

    @JsonProperty("post_id")
    private long postId;

    @JsonProperty("user_id")
    private long userId;

    @JsonProperty("from_tag")
    private String fromTag;

    @JsonProperty("content")
    private String content;

    @JsonProperty("tags")
    private String tags;
}
