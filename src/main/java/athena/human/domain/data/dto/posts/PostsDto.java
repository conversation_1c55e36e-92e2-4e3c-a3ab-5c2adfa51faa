package athena.human.domain.data.dto.posts;

import athena.human.domain.data.BaseDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class PostsDto extends BaseDto {

    @JsonProperty("author")
    private String author;

    @JsonProperty("content")
    private String content;

    @JsonProperty("from_agent")
    private boolean fromAgent;

    @JsonProperty("media")
    private String media;

    @JsonProperty("poll")
    private String poll;

    @JsonProperty("hashtags")
    private String hashtags;

    @JsonProperty("mentions")
    private String mentions;

    @JsonProperty("is_public")
    private boolean isPublic;

    @JsonProperty("likes")
    private int likes;

    @JsonProperty("shares")
    private int shares;

    @JsonProperty("user_id")
    private long userId;
}
