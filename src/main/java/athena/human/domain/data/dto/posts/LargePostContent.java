package athena.human.domain.data.dto.posts;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

@Data
@NoArgsConstructor
@Document(collection = "large_post_content")
public class LargePostContent {
    
    @Id
    private String id;
    
    @Field("post_id")
    private Long postId;
    
    @Field("content")
    private String content;
    
    @Field("author")
    private String author;
    
    @Field("media")
    private String media;
    
    @Field("poll")
    private String poll;
    
    @Field("hashtags")
    private String hashtags;
    
    @Field("mentions")
    private String mentions;
    
    @Field("user_id")
    private Long userId;
    
    @Field("created_at")
    private Date createdAt;
    
    @Field("updated_at")
    private Date updatedAt;
    
    public LargePostContent(Long postId, String content, String author, String media, 
                           String poll, String hashtags, String mentions, Long userId) {
        this.postId = postId;
        this.content = content;
        this.author = author;
        this.media = media;
        this.poll = poll;
        this.hashtags = hashtags;
        this.mentions = mentions;
        this.userId = userId;
        this.createdAt = new Date();
        this.updatedAt = new Date();
    }
}
