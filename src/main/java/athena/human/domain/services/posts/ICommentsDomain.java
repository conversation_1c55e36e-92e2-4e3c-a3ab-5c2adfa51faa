package athena.human.domain.services.posts;

import athena.human.domain.data.dto.posts.CommentsDto;

import java.util.List;

public interface ICommentsDomain {
    CommentsDto getComment(Long id);
    List<CommentsDto> getCommentsForPost(Long postId);
    List<CommentsDto> getAllComments();
    CommentsDto createComment(CommentsDto commentsDto);
    CommentsDto updateComment(Long id, CommentsDto commentsDto);
    void deleteComment(Long id);
}
