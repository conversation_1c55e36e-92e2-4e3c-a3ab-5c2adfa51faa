package athena.human.domain.serviceimpl.posts;

import athena.human.domain.data.dto.posts.PostsDto;
import athena.human.domain.data.mappers.posts.PostsDataMapper;
import athena.human.domain.services.posts.IPostsDomain;
import athena.human.entity.posts.Posts;
import athena.human.repository.mappers.posts.PostsEntityMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class PostsDomain implements IPostsDomain {

    @Autowired
    private PostsEntityMapper postsEntityMapper;

    private final PostsDataMapper postsDataMapper = PostsDataMapper.INSTANCE;

    @Override
    public PostsDto getPost(Long id) {
        return postsDataMapper.toDto(postsEntityMapper.findById(id));
    }

    @Override
    public List<PostsDto> getAllPosts() {
        return postsEntityMapper.findAll().stream()
                .map(postsDataMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public PostsDto createPost(PostsDto postsDto) {
        Posts post = postsDataMapper.toEntity(postsDto);
        postsEntityMapper.insert(post);
        return postsDataMapper.toDto(post);
    }

    @Override
    public PostsDto updatePost(Long id, PostsDto postsDto) {
        Posts post = postsDataMapper.toEntity(postsDto);
        post.setId(id);
        postsEntityMapper.update(post);
        return postsDataMapper.toDto(post);
    }

    @Override
    public void deletePost(Long id) {
        postsEntityMapper.deleteById(id);
    }
}
