package athena.human.domain.serviceimpl.posts;

import athena.human.domain.data.dto.posts.CommentsDto;
import athena.human.domain.data.mappers.posts.CommentsDataMapper;
import athena.human.domain.services.posts.ICommentsDomain;
import athena.human.entity.posts.Comments;
import athena.human.repository.mappers.posts.CommentsEntityMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CommentsDomain implements ICommentsDomain {

    @Autowired
    private CommentsEntityMapper commentsEntityMapper;

    private final CommentsDataMapper commentsDataMapper = CommentsDataMapper.INSTANCE;

    @Override
    public CommentsDto getComment(Long id) {
        return commentsDataMapper.toDto(commentsEntityMapper.findById(id));
    }

    @Override
    public List<CommentsDto> getCommentsForPost(Long postId) {
        return commentsEntityMapper.findByPostId(postId).stream()
                .map(commentsDataMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<CommentsDto> getAllComments() {
        return commentsEntityMapper.findAll().stream()
                .map(commentsDataMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public CommentsDto createComment(CommentsDto commentsDto) {
        Comments comment = commentsDataMapper.toEntity(commentsDto);
        commentsEntityMapper.insert(comment);
        return commentsDataMapper.toDto(comment);
    }

    @Override
    public CommentsDto updateComment(Long id, CommentsDto commentsDto) {
        Comments comment = commentsDataMapper.toEntity(commentsDto);
        comment.setId(id);
        commentsEntityMapper.update(comment);
        return commentsDataMapper.toDto(comment);
    }

    @Override
    public void deleteComment(Long id) {
        commentsEntityMapper.deleteById(id);
    }
}
