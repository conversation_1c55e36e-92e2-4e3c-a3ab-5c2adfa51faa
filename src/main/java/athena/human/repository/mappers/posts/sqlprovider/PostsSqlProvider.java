package athena.human.repository.mappers.posts.sqlprovider;

import athena.human.entity.posts.Posts;
import org.apache.ibatis.jdbc.SQL;

public class PostsSqlProvider {

    public String insert(Posts post) {
        return new SQL()
                .INSERT_INTO("posts")
                .VALUES("author", "#{author}")
                .VALUES("content", "#{content}")
                .VALUES("from_agent", "#{fromAgent}")
                .VALUES("media", "#{media}")
                .VALUES("poll", "#{poll}")
                .VALUES("hashtags", "#{hashtags}")
                .VALUES("mentions", "#{mentions}")
                .VALUES("is_public", "#{isPublic}")
                .VALUES("likes", "#{likes}")
                .VALUES("shares", "#{shares}")
                .VALUES("user_id", "#{userId}")
                .toString();
    }

    public String update(Posts post) {
        return new SQL()
                .UPDATE("posts")
                .SET("author = #{author}")
                .SET("content = #{content}")
                .SET("from_agent = #{fromAgent}")
                .SET("media = #{media}")
                .SET("poll = #{poll}")
                .SET("hashtags = #{hashtags}")
                .SET("mentions = #{mentions}")
                .SET("is_public = #{isPublic}")
                .SET("likes = #{likes}")
                .SET("shares = #{shares}")
                .WHERE("id = #{id}")
                .toString();
    }
}
