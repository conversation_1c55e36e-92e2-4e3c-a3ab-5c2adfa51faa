package athena.human.repository.mappers.posts;

import athena.human.entity.posts.Posts;
import athena.human.repository.mappers.posts.sqlprovider.PostsSqlProvider;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface PostsEntityMapper {

    @Select("SELECT * FROM posts WHERE id = #{id}")
    Posts findById(Long id);

    @Select("SELECT * FROM posts")
    List<Posts> findAll();

    @InsertProvider(type = PostsSqlProvider.class, method = "insert")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Posts post);

    @UpdateProvider(type = PostsSqlProvider.class, method = "update")
    void update(Posts post);

    @Delete("DELETE FROM posts WHERE id = #{id}")
    void deleteById(Long id);
}
