package athena.human.repository.mappers.posts;

import athena.human.entity.posts.Comments;
import athena.human.repository.mappers.posts.sqlprovider.CommentsSqlProvider;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface CommentsEntityMapper {

    @Select("SELECT * FROM comments WHERE id = #{id}")
    Comments findById(Long id);

    @Select("SELECT * FROM comments WHERE post_id = #{postId}")
    List<Comments> findByPostId(Long postId);

    @Select("SELECT * FROM comments")
    List<Comments> findAll();

    @InsertProvider(type = CommentsSqlProvider.class, method = "insert")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    void insert(Comments comment);

    @UpdateProvider(type = CommentsSqlProvider.class, method = "update")
    void update(Comments comment);

    @Delete("DELETE FROM comments WHERE id = #{id}")
    void deleteById(Long id);
}
