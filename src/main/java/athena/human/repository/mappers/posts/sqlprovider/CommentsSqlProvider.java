package athena.human.repository.mappers.posts.sqlprovider;

import athena.human.entity.posts.Comments;
import org.apache.ibatis.jdbc.SQL;

public class CommentsSqlProvider {

    public String insert(Comments comment) {
        return new SQL()
                .INSERT_INTO("comments")
                .VALUES("post_id", "#{postId}")
                .VALUES("user_id", "#{userId}")
                .VALUES("from_tag", "#{fromTag}")
                .VALUES("content", "#{content}")
                .VALUES("tags", "#{tags}")
                .toString();
    }

    public String update(Comments comment) {
        return new SQL()
                .UPDATE("comments")
                .SET("from_tag = #{fromTag}")
                .SET("content = #{content}")
                .SET("tags = #{tags}")
                .WHERE("id = #{id}")
                .toString();
    }
}
