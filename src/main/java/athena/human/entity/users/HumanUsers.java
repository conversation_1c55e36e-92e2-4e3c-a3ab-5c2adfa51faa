package athena.human.entity.users;

import athena.human.entity.BaseEntity;
import lombok.*;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class HumanUsers extends BaseEntity {
    private String userPlatName;
    private String userNickname;
    private String userRealName;
    private String userLocation;
    private String occupationTitle;
    private HumanUserProperties userProperties;
}
