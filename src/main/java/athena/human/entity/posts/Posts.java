package athena.human.entity.posts;

import athena.human.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class Posts extends BaseEntity {
    private String author;
    private String content;
    private boolean fromAgent;
    private String media;
    private String poll; // JSON string for poll data
    private String hashtags; // Stored as a comma-separated string
    private String mentions; // Stored as a comma-separated string
    private boolean isPublic;
    private int likes;
    private int shares;
    private long userId;
}
